<template>
  <div class="app">
    <!-- <div class="head">
          <div class="titles">话题管理</div>
          <div class="title1">Pages/宝妈社区/话题管理</div>
      </div> -->
    <div class="content">
      <div class="contentHead">
        <h2 class="title2">客服管理</h2>
        <div class="contentHead_1">
          <el-button type="primary" style="height: 36px; margin: 20px 0 28px 0" @click="add">新增客服</el-button>
          <!-- <div class="contentHeadTitle">最多添加四位客服</div> -->
        </div>
        <el-table v-loading="loading" :data="listData" class="table">
          <el-table-column label="客服" prop="kfNick" :show-overflow-tooltip="true" align="center" width="200">
            <template slot-scope="scope">
              <div style="display: flex; align-items: center; justify-content: center; text-align: left;">
                <img :src="scope.row.avatar" alt="" style="width: 60px; height: 60px; border-radius: 50%; margin-right: 10px;" />
                <div>
                  <div>{{ scope.row.kfNick }}</div>
                  <!-- <div>{{ scope.row.kfWx }}</div> -->
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="最大同时接待量" prop="maxConcurrentChats" align="center" />
          <el-table-column label="当前接待量" prop="currentChats" align="center" />
          <el-table-column label="累计服务人数" prop="totalServed" align="center" />
          <el-table-column label="状态" prop="status" :formatter="formatStatus" align="center" />
          <el-table-column label="设置时间" prop="createTime" align="center" width="160" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
              <!-- Add edit button if needed -->
              <!-- <el-button size="mini" type="text" @click="handleEdit(scope.row)">修改</el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <div class="block">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :page-sizes="[10, 20, 30, 40]" :page-size="100" layout="total, sizes, prev, pager, next, jumper"
            :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog title="新增客服" :visible.sync="dialogFormVisible">
      <!-- <el-form :model="form">
        <el-form-item label="选择用户">
          <el-select v-model="form.region" placeholder="请选择用户">
            <el-option
              :label="item.kfNick"
              :value="item.userId"
              v-for="(item, index) in listUser"
              :key="index"
              @click.native="getCorpAndDept(item)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form> -->
      <el-select v-model="selectedList" multiple filterable remote reserve-keyword placeholder="请输入客服名称搜索并选择"
        :remote-method="remoteMethod" :loading="loading" style="width: 100%; margin-bottom: 15px;">
        <el-option v-for="item in options" :key="item.userId" :label="item.kfNick + (item.kfWx ? ' (' + item.kfWx + ')' : '')" :value="item.userId" :disabled="addedUserIds.includes(item.userId)">
           <!-- Optional: Enhance display within the dropdown -->
           <span style="float: left">{{ item.kfNick }}</span>
           <span style="float: right; color: #8492a6; font-size: 13px">{{ item.kfWx }}</span>
        </el-option>
      </el-select>
      <div v-if="selectedList.length > 0">
        <h3>已选择</h3>
        <div class="selected1">
          <!-- Loop through selectedList which now contains only user IDs -->
          <div class="selected" v-for="userId in selectedList" :key="userId">
             <!-- Find the user object using the helper function -->
            <template v-if="getUserById(userId)">
               <img :src="getUserById(userId).avatar" alt="" style="width: 48px; height: 48px; border-radius: 50%;" />
               <div class="selectedTitle">
                 <div class="selectedTitle1">{{ getUserById(userId).kfNick }}</div>
                 <div class="selectedTitle2">{{ getUserById(userId).kfWx }}</div>
               </div>
            </template>
             <!-- The delete image is removed as el-select handles removal -->
            <!-- <img src="http://cdn.xiaodingdang1.com/2024/09/12/1178c084adee4e7392e9dcf16502a4a1.png" alt=""
              style="width: 20px; height: 20px" @click="del(index)" /> -->
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  save,
  delTable,
  assignUserRole,
} from "@/api/platform/customerManagement";
import { getList as getWechatUserList } from "@/api/platform/wechatUser";
import { getRoleList } from "@/api/platform/role";
export default {
  name: "app",
  data() {
    return {
      listUser: [], // Consider removing if not used elsewhere
      dialogFormVisible: false,
      // form: { // Removed unused form property
      //   region: "",
      // },
      loading: false, // Initialize loading state for API calls and remote search
      total: 0,
      topicId: "", // Consider removing if not used
      ruleForm: {
        pageSize: 10,
        pageNum: 1,
      },
      listData: [],
      consultationImg: "", // Consider removing if not used
      selectedList: [], // Now stores selected user IDs directly from el-select
      options: [], // Options for the select dropdown
      // loading: false, // Duplicate loading property removed
      listStates: [], // Stores the full list of user objects for searching and display
      // states: [], // Consider removing if not used
      customerServiceRoleId: null, // 客服角色ID
    };
  },
  computed: { // Add computed property
    addedUserIds() {
      // Extract userId from listData, ensuring userId exists and handling potential variations in property names
      return this.listData.map(user => user.userId || user.id).filter(id => id != null);
    }
  },
  created() {
    this.getList();
    this.queryUserCustomer();
    this.getCustomerServiceRole();
    // this.loading = true; // loading is managed within methods
  },
  mounted() { },
  methods: {
    remoteMethod(query) {
      if (query !== "") {
        this.loading = true; // Show loading indicator during search
        // Simulating API call delay or filtering logic
        setTimeout(() => {
          this.loading = false;
          this.options = this.listStates.filter((item) => {
            // Ensure item.kfNick exists and is a string before calling toLowerCase
            const nick = item.kfNick || '';
            const wx = item.kfWx || '';
            return nick.toLowerCase().includes(query.toLowerCase()) || wx.toLowerCase().includes(query.toLowerCase());
          });
        }, 200);
      } else {
        // Show a subset of users or all users when the query is empty
        this.options = this.listStates; // Example: show first 50 users, adjust as needed
      }
    },
    cancel() {
      this.selectedList = []; // Clear the selection
      this.options = []; // Clear options
      this.dialogFormVisible = false;
    },
    // del(index) { // Removed unused del method, el-select handles removal in multiple mode
    //   this.selectedList.splice(index, 1);
    // },
    // getCorpAndDept(e) { // Removed unused getCorpAndDept method, el-select handles selection in multiple mode
    // },
    confirm() {
      this.onSubmit();
    },
    add() {
      //新增
      this.selectedList = []; // Clear previous selection when opening dialog
      this.options = this.listStates; // Initialize options for the dropdown
      this.dialogFormVisible = true; // Use direct assignment
    },
    inquire() {
      //查询
      this.ruleForm.pageNum = 1; // Reset to first page on new search
      this.getList();
    },
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val;
      this.getList();
    },
    getList() {
      //列表
      this.loading = true;
      page(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.listData = res.rows.map((item)=>{
            return {
              ...item,
              kfNick: item.nickname || '未知昵称', // Provide default if null/undefined
              kfWx: item.openid || '未知微信号', // Provide default if null/undefined
              avatar: item.avatarUrl || 'default_avatar.png', // Provide a default avatar URL if needed
              kfOpenid: item.openid || '', // Provide default if null/undefined
              createTime: item.createdTime || '未知时间', // Provide default if null/undefined
              // Add new fields, assuming they exist in the API response 'item'
              maxConcurrentChats: item.maxConcurrentChats ?? 'N/A', // Use nullish coalescing for default
              currentChats: item.currentChats ?? 'N/A',
              totalServed: item.totalServed ?? 'N/A',
              status: item.status ?? 0, // Default to 0 (Offline) if not provided
            };
          });
          this.total = res.total;
        } else {
          this.$message.error(res.msg || '获取列表失败'); // Use error message
        }
      }).catch(err => {
        console.error("Error fetching list:", err);
        this.$message.error('请求列表时出错');
      }).finally(() => {
         this.loading = false; // Ensure loading is always turned off
      });
    },
    onSubmit() {
      //确认添加
      if (this.selectedList.length === 0) { // Use strict equality
        this.$message("请先选择需要新增的客服");
        return;
      }
      // Map selectedList (which contains user IDs) to the desired payload structure
      const payload = this.selectedList.map(userId => {
         // Find the full user object from listStates based on userId
         const user = this.listStates.find(u => u.userId === userId);
         // Return the object with specified fields if user is found
         if (user) {
           return {
             userId: user.userId,
             openid: user.kfOpenid, // Map kfOpenid to openid
             nickname: user.kfNick, // Map kfNick to nickname
             avatarUrl: user.avatar // Map avatar to avatarUrl
           };
         } else {
           // Handle case where user might not be found (though unlikely)
           console.warn(`User with ID ${userId} not found in listStates.`);
           return null; // Return null for users not found
         }
      }).filter(user => user !== null); // Filter out any null entries

      if (payload.length === 0) {
          this.$message.error("无法找到选择的客服信息或选择的客服无效");
          return;
      }

      this.loading = true;
      save(payload).then((res) => { // Send the mapped payload
        if (res.code == 200) {
          // 客服添加成功后，为每个用户分配客服角色
          this.assignCustomerServiceRole(payload);
          this.$message.success(res.msg || '添加成功'); // Use success message
          this.dialogFormVisible = false;
          this.selectedList = []; // Clear selection after successful save
          this.getList(); // Refresh the main list
        } else {
          this.$message.error(res.msg || '添加失败');
        }
      }).catch(err => {
          console.error("Error saving customer:", err);
          this.$message.error('提交时出错');
      }).finally(() => {
          this.loading = false; // Ensure loading is always turned off
      });
    },
    handleDelete(row) {
      //删除
      this.$confirm(`确定删除客服 ${row.kfNick || ''} 吗？`, "提示", { // Improved confirmation message
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.loading = true; // Set loading before API call
          delTable(row.id).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.msg || '删除成功');
              this.getList(); // Refresh list after successful deletion
            } else {
              this.$message.error(res.msg || '删除失败');
            }
          }).catch(err => {
             console.error("Error deleting customer:", err);
             this.$message.error('删除时出错');
          }).finally(() => {
             this.loading = false; // Set loading to false after API call completes
          });
        })
        .catch(() => {
          // User cancelled the deletion
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    queryUserCustomer() {
      //查询微信用户（选择下拉框）
      this.loading = true; // Indicate loading while fetching users
      getWechatUserList().then((res) => {
        if (res.code == 200 && Array.isArray(res.data)) {
          // this.listUser = res.data; // listUser seems unused, consider removing
          this.listStates = res.data.map((item) => {
            // Ensure all properties are strings or handle potential null/undefined values
            return {
              userId: String(item.userId || ''), // Ensure userId is a string and exists
              kfNick: String(item.nickname || '未知昵称'), // Provide default if null/undefined
              kfWx: String(item.tel || ''), // Default to empty string if no Wx ID
              tenantId: String(item.tenantId || ''),
              kfOpenid: String(item.openid || ''),
              avatar: String(item.avatar || 'default_avatar.png'), // Provide a default avatar URL if needed
            };
          }).filter(user => user.userId); // Ensure only users with an ID are included
          this.options = this.listStates; // Initialize options after data is loaded
        } else {
          this.$message.error(res.msg || '查询用户失败');
          this.listStates = []; // Ensure listStates is an empty array on failure
          this.options = [];
        }
      }).catch(err => {
         console.error("Error fetching wechat users:", err);
         this.$message.error('查询用户时出错');
         this.listStates = []; // Ensure listStates is an empty array on error
         this.options = [];
      }).finally(() => {
         this.loading = false; // Turn off loading indicator
      });
    },
     // Helper function to get user object by ID from listStates
     getUserById(userId) {
        return this.listStates.find(user => user.userId === userId);
     },
     // Add formatter function for status
    formatStatus(row, column, cellValue) {
      const statusMap = {
        0: '离线',
        1: '在线',
        2: '忙碌'
      };
      return statusMap[cellValue] || '未知'; // Return '未知' for unexpected values
    },
    // 获取客服角色
    getCustomerServiceRole() {
      getRoleList().then((res) => {
        if (res.code == 200 && Array.isArray(res.data)) {
          // 查找名称为"客服"的角色
          const customerServiceRole = res.data.find(role =>
            role.roleName === '客服' || role.roleName === 'customer_service' || role.roleCode === 'customer_service'
          );
          if (customerServiceRole) {
            this.customerServiceRoleId = customerServiceRole.roleId;
            console.log('找到客服角色ID:', this.customerServiceRoleId);
          } else {
            console.warn('未找到客服角色，请确保系统中存在客服角色');
          }
        } else {
          console.error('获取角色列表失败:', res.msg);
        }
      }).catch(err => {
        console.error('获取角色列表时出错:', err);
      });
    },
    // 为用户分配客服角色
    assignCustomerServiceRole(users) {
      if (!this.customerServiceRoleId) {
        console.warn('客服角色ID未找到，跳过角色分配');
        return;
      }

      // 为每个用户分配客服角色
      const roleAssignPromises = users.map(user => {
        const roleData = {
          userId: user.userId,
          roleId: this.customerServiceRoleId
        };
        return assignUserRole(roleData).then((res) => {
          if (res.code == 200) {
            console.log(`成功为用户 ${user.nickname} 分配客服角色`);
          } else {
            console.error(`为用户 ${user.nickname} 分配角色失败:`, res.msg);
          }
        }).catch(err => {
          console.error(`为用户 ${user.nickname} 分配角色时出错:`, err);
        });
      });

      // 等待所有角色分配完成
      Promise.all(roleAssignPromises).then(() => {
        console.log('所有用户角色分配完成');
      }).catch(err => {
        console.error('角色分配过程中出现错误:', err);
      });
    }
  },
};
</script>

<style scoped lang="scss">
.viewLocations {
  display: flex;
  align-items: center;
}

.BackgroundPicture {
  display: flex;
}

.upload {
  margin-left: 20px;
}

.uploadTitle {
  display: flex;
  align-items: center;
}

.consultation {
  padding: 20px 24px;
  background: #ffff;
  border-radius: 10px;
  margin: 0px 24px;
}

.app {
  background: #f0f1f5;
  padding-bottom: 30px;
}

.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;

  .titles {
    font-size: 22px;
    font-weight: bold;
  }

  .title1 {
    font-size: 14px;
  }
}

.content {
  margin: 20px 20px;

  .contentHead {
    background: #ffff;
    padding: 5px 14px;
    border-radius: 10px;

    .title2 {
      color: #17191a;
      font-size: 18px;
      font-weight: bold;
    }
  }
}

.contentHead_1 {
  display: flex;
  align-items: center;

  .contentHeadTitle {
    font-size: 14px;
    color: #7c7d81;
    margin-left: 8px;
  }
}

.contentHeads {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.roomMessage {
  display: flex;
}

.sel {
  display: flex;
  align-items: center;
  margin-right: 10px;

  .selTitle {
    width: 30%;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
  }
}

#cars {
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  color: #7c7d81;
  font-size: 14px;
  width: 179px;
  height: 32px;
  margin-left: 6px;
}

.block {
  text-align: right;
  margin-top: 20px;
}

.selected1 {
  width: 100%; // Make it full width for better wrapping
  display: flex;
  flex-wrap: wrap;
  gap: 10px; // Add gap between items for spacing

  .selected {
    display: flex;
    padding: 8px; // Adjusted padding
    background: #f8f9fa;
    border: 1px solid #eee; // Add a light border
    border-radius: 6px; // Slightly smaller radius
    align-items: center;
    // Use flex-basis for better control with gap
    flex-basis: calc(50% - 5px); // Two items per row, accounting for 10px gap
    box-sizing: border-box; // Include padding and border in width calculation
    margin-bottom: 0; // Remove margin-bottom as gap handles spacing

    img:first-of-type { // Style the avatar image
        width: 40px; // Slightly smaller avatar
        height: 40px;
        margin-right: 8px;
        border-radius: 50%; // Make avatar circular
        object-fit: cover; // Ensure avatar image covers the area well
    }

    .selectedTitle {
      font-size: 14px;
      color: #333; // Darker color for better readability
      flex-grow: 1; // Allow title to take available space
      overflow: hidden; // Prevent text overflow issues

      .selectedTitle1 {
         font-weight: bold; // Keep nickname bold
         color: #000;
         white-space: nowrap; // Prevent wrapping
         overflow: hidden;
         text-overflow: ellipsis; // Add ellipsis if name is too long
      }

      .selectedTitle2 {
        margin-top: 4px; // Reduced margin
        font-size: 12px; // Smaller font size for secondary info
        color: #666; // Lighter color for secondary info
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
     // The delete image/icon is removed as el-select handles removal in multiple mode
  }
}

/* Style adjustments for el-select */
.el-select {
  width: 100%; /* Make select full width */
}

/* Ensure dialog content has enough space */
.el-dialog__body {
  padding: 20px 20px 30px 20px; /* Adjust padding as needed */
}

/* Style for the selected items display area */
div > h3 {
    margin-top: 15px; /* Space above the '已选择' title */
    margin-bottom: 10px; /* Space below the title */
}

/* Responsive adjustments for selected items display */
@media (max-width: 600px) {
  .selected1 .selected {
    flex-basis: 100%; /* Stack items vertically on smaller screens */
  }
}
</style>
